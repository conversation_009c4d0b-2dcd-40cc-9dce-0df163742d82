# Universal Reviews Functionality Setup

## Overview
ეს ფუნქციონალი ამატებს "Review Us" სექციას ყველა enquiry მოდალში (Awards, Conferences, Courses, Festivals) და ქმნის admin panel-ში უნივერსალურ Reviews განყოფილებას.

## Files Created/Modified

### 1. Database & Models
- `setup_reviews.sql` - SQL ფაილი reviews ცხრილისა და admin panel კონფიგურაციისთვის
- `app/Models/Review.php` - Review Model

### 2. Controllers
- `app/Http/Controllers/ContactController.php` - განახლებული review მონაცემების შესანახად
- `app/Http/Controllers/Shuttle/ReviewsController.php` - Admin panel controller

### 3. Views
- `resources/views/awards/show.blade.php` - განახლებული Review Us სექციით
- `resources/views/shuttle/reviews/index.blade.php` - Admin reviews list
- `resources/views/shuttle/reviews/edit_add.blade.php` - Admin review edit/add
- `resources/views/shuttle/reviews/show.blade.php` - Admin review details

## Installation Steps

### 1. Execute SQL
```bash
mysql -u root -p your_database_name < setup_reviews.sql
```

### 2. Features Added

#### Frontend (All Sections)
- Awards, Conferences, Courses, Festivals-ის enquiry მოდალებში "Review Us" სექცია
- მომხმარებელს შეუძლია დატოვოს review text ყველა სექციისთვის
- ყველა review ინახება `reviews` ცხრილში section type-ით

#### Admin Panel
- ახალი "Reviews" განყოფილება admin მენიუში
- Reviews-ის სია ფილტრებით:
  - Section Type (Award/Conference/Course/Festival)
  - Status (Pending/Approved/Rejected)
  - Rating (1-5 stars)
- Review-ის დეტალური ნახვა და რედაქტირება
- Status-ის შეცვლის შესაძლებლობა

### 3. Database Structure
```sql
reviews table:
- id, name, surname, email, phone, company, job_title
- message, review_text, rating
- model_type, model_id (polymorphic relation)
- section_type (award/conference/course/festival)
- section_id, section_title
- status (pending/approved/rejected)
- timestamps
```

### 4. How It Works
1. მომხმარებელი ნებისმიერ გვერდზე (awards/conferences/courses/festivals) ავსებს enquiry ფორმას review text-ით
2. ContactController ავტომატურად განსაზღვრავს section type-ს და ინახავს enquiry-ს და review-ს
3. Admin panel-ში ჩანს ყველა review ფილტრებით (section type, status, rating)
4. Admin-ს შეუძლია status შეცვლა (pending → approved/rejected)

### 5. Admin Access
- URL: `/mypanel/reviews`
- მენიუში: "Reviews" (ვარსკვლავის აიკონით)

## Notes
- Reviews ქმნება frontend-იდან ყველა სექციისთვის (awards/conferences/courses/festivals)
- Admin panel-ში მხოლოდ ნახვა/რედაქტირება/წაშლა
- ყველა review იწყება "pending" status-ით
- მიგრაციას არ ვიყენებთ - მხოლოდ SQL ფაილი
- სამომავლოდ შეგიძლია დაამატო review ფუნქციონალი ნებისმიერ სექციაში

## Future Implementation
როცა დაამატებ review ფუნქციონალს სხვა სექციებში:

1. **Conferences:** დაამატე Review Us სექცია conference enquiry მოდალში
2. **Courses:** დაამატე Review Us სექცია course enquiry მოდალში
3. **Festivals:** დაამატე Review Us სექცია festival enquiry მოდალში

ContactController უკვე მზადაა ყველა სექციისთვის!
