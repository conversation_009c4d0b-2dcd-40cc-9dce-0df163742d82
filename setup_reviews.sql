-- 1. Create reviews table (universal for all sections)
CREATE TABLE `reviews` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `surname` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `company` varchar(255) DEFAULT NULL,
  `job_title` varchar(255) DEFAULT NULL,
  `more_info` varchar(255) DEFAULT NULL,
  `know_by` varchar(255) DEFAULT NULL,
  `message` text DEFAULT NULL,
  `review_text` text DEFAULT NULL,
  `rating` int(11) DEFAULT NULL,
  `model_type` varchar(255) DEFAULT NULL COMMENT 'App\\Models\\Awward, App\\Models\\Conference, App\\Models\\Course, App\\Models\\Festival',
  `model_id` bigint(20) unsigned DEFAULT NULL,
  `section_type` enum('award','conference','course','festival') DEFAULT NULL COMMENT 'Type of section for easy filtering',
  `section_id` bigint(20) unsigned DEFAULT NULL COMMENT 'ID of the specific item',
  `section_title` varchar(255) DEFAULT NULL COMMENT 'Title of the specific item',
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `reviews_model_type_model_id_index` (`model_type`,`model_id`),
  KEY `reviews_section_type_index` (`section_type`),
  KEY `reviews_section_id_index` (`section_id`),
  KEY `reviews_status_index` (`status`),
  KEY `reviews_created_at_index` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Add Reviews to admin panel
INSERT INTO `shuttle_scaffold_interfaces` (`name`, `icon`, `slug`, `display_name_singular`, `display_name_plural`, `migration`, `model`, `translation_model`, `controller`, `menuable`, `by_shuttle`, `created_at`, `updated_at`) VALUES
('reviews', 'iconsmind-Star', 'reviews', 'Review', 'Reviews', NULL, 'App\\Models\\Review', NULL, 'App\\Http\\Controllers\\Shuttle\\ReviewsController', 1, 0, NOW(), NOW());

-- 3. Get the inserted ID and use it for scaffold interface rows
SET @scaffold_id = LAST_INSERT_ID();

-- 4. Add scaffold interface rows for Reviews
INSERT INTO `shuttle_scaffold_interface_rows` (`rowable_type`, `rowable_id`, `field`, `type`, `display_name`, `required`, `browse`, `read`, `edit`, `add`, `delete`, `details`, `ord`, `parent_id`) VALUES
('Sina\\Shuttle\\Models\\ScaffoldInterface', @scaffold_id, 'id', 'number', 'ID', 0, 1, 1, 0, 0, 0, '{}', 1, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', @scaffold_id, 'name', 'text', 'Name', 1, 1, 1, 1, 1, 0, '{}', 2, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', @scaffold_id, 'surname', 'text', 'Surname', 1, 1, 1, 1, 1, 0, '{}', 3, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', @scaffold_id, 'email', 'text', 'Email', 1, 1, 1, 1, 1, 0, '{}', 4, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', @scaffold_id, 'phone', 'text', 'Phone', 0, 1, 1, 1, 1, 0, '{}', 5, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', @scaffold_id, 'company', 'text', 'Company', 0, 1, 1, 1, 1, 0, '{}', 6, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', @scaffold_id, 'job_title', 'text', 'Job Title', 0, 1, 1, 1, 1, 0, '{}', 7, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', @scaffold_id, 'section_type', 'select_dropdown', 'Section Type', 1, 1, 1, 1, 1, 0, '{"default":"award","options":{"award":"Award","conference":"Conference","course":"Course","festival":"Festival"}}', 8, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', @scaffold_id, 'section_title', 'text', 'Title', 0, 1, 1, 1, 1, 0, '{}', 9, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', @scaffold_id, 'message', 'text_area', 'Original Message', 0, 0, 1, 1, 1, 0, '{}', 10, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', @scaffold_id, 'review_text', 'text_area', 'Review Text', 0, 1, 1, 1, 1, 0, '{}', 11, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', @scaffold_id, 'rating', 'number', 'Rating (1-5)', 0, 1, 1, 1, 1, 0, '{"min":1,"max":5}', 12, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', @scaffold_id, 'status', 'select_dropdown', 'Status', 1, 0, 1, 0, 0, 0, '{"default":"pending","options":{"pending":"Pending","approved":"Approved","rejected":"Rejected"}}', 13, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', @scaffold_id, 'created_at', 'timestamp', 'Created At', 0, 1, 1, 0, 0, 0, '{}', 14, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', @scaffold_id, 'updated_at', 'timestamp', 'Updated At', 0, 0, 0, 0, 0, 0, '{}', 15, 0);

-- 5. Add Reviews to main menu (skip this if menu structure is different)
-- You can add this manually from admin panel: /mypanel/menu
-- INSERT INTO `shuttle_menu_items` (`menu_id`, `label`, `url`, `target`, `icon_class`, `color`, `parent_id`, `ord`, `created_at`, `updated_at`, `menuable_type`, `menuable_id`, `lft`, `rgt`, `depth`) VALUES
-- (1, 'Reviews', NULL, '_self', 'iconsmind-Star', NULL, 0, 999, NOW(), NOW(), 'Sina\\Shuttle\\Models\\ScaffoldInterface', @scaffold_id, 999, 1000, 0);
