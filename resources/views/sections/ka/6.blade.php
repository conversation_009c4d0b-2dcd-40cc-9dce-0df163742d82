<section class="section pt-30px"><div class="container"><div class="row"><div class="col-md-12"><div class="section-title"><span>@lang("common.news_small_title")</span><h1>@lang("common.news_big_title")</h1></div></div></div></div></section><section class="section news pt-0"><div class="container"><div class="row">@foreach($data as $item_of_model)<article class="pbmit-blog-style-1 col-md-4"><div class="post-item"><div class="pbmit-featured-container"><div class="pbmit-featured-wrapper"><img src="{{Storage::url(data_get($item_of_model,"image"))}}" class="img-fluid" alt="{{data_get($item_of_model,"title")}}"></div></div><div class="pbminfotech-box-content"><div class="pbmit-meta-date-wrapper"><span class="pbmit-day">{{data_get($item_of_model,"created_at")->locale(LaravelLocalization::getCurrentLocale())->isoFormat("D")}}</span><span>{{data_get($item_of_model,"created_at")->locale(LaravelLocalization::getCurrentLocale())->isoFormat("MMMM")}}</span></div><h3 class="pbmit-post-title"><a href="{{$url}}/{{data_get($item_of_model,"id")}}">{{data_get($item_of_model,"title")}}</a></h3><div class="pbminfotech-box-desc"><div class="pbmit-read-more-link"><a href="{{$url}}/{{data_get($item_of_model,"id")}}"><span>Read More</span></a></div></div></div></div></article>@endforeach</div></div></section>