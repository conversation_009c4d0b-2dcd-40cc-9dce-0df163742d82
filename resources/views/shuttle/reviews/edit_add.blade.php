@extends('shuttle::admin')

@section('main')
<div class="row">
    <div class="col-md-12">
        <x-shuttle-form
                :action="$dataTypeContent->id ? route('shuttle.scaffold_interface.update', ['scaffold_interface' => $scaffold_interface, 'id' => $dataTypeContent->id, 'lang' => $lang]) : route('shuttle.scaffold_interface.store',$scaffold_interface)"
                :scaffold-interface-rows="$scaffold_interface->rows"
                :data-type-content="$dataTypeContent"
                :edit="$dataTypeContent->id ? true : false"
        >
        </x-shuttle-form>
    </div>
</div>

<style>
.review-details {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-top: 10px;
}

.rating-display {
    color: #ffc107;
    font-size: 18px;
}

.status-badge {
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.status-pending {
    background: #ffeaa7;
    color: #d63031;
}

.status-approved {
    background: #55a3ff;
    color: white;
}

.status-rejected {
    background: #fd79a8;
    color: white;
}
</style>

@if($dataTypeContent->id)
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add rating display for existing reviews
    const ratingField = document.querySelector('input[name="rating"]');
    if (ratingField && ratingField.value) {
        const rating = parseInt(ratingField.value);
        const stars = '★'.repeat(rating) + '☆'.repeat(5 - rating);
        const ratingDisplay = document.createElement('div');
        ratingDisplay.className = 'rating-display';
        ratingDisplay.innerHTML = `Rating: ${stars} (${rating}/5)`;
        ratingField.parentNode.appendChild(ratingDisplay);
    }
});
</script>
@endif

@stop
