@extends('shuttle::admin')

@section('breadcrumbs')
    @php
        $breadCrumbs = [
            'Home' => route('shuttle.index'),
            'Reviews' => route('shuttle.scaffold_interface.index', $scaffoldInterface),
            'Review Details' => '#'
        ]
    @endphp
    <div class="pageTitle">
        <div class="pageTitle-title">
            <h1>Review Details</h1>
        </div>
        <div class="pageTitle-down">
            <ul>
                @foreach($breadCrumbs as $bread => $route)
                    <li class="breadcrumb-item @if($loop->last) active @endif">
                        <a href="{{$route}}">{{$bread}}</a>
                    </li>
                @endforeach
            </ul>
        </div>
    </div>
@stop

@section('main')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h4>Review Information</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Personal Information</h5>
                        <p><strong>Name:</strong> {{ $dataTypeContent->name }} {{ $dataTypeContent->surname }}</p>
                        <p><strong>Email:</strong> {{ $dataTypeContent->email }}</p>
                        <p><strong>Phone:</strong> {{ $dataTypeContent->phone }}</p>
                        <p><strong>Company:</strong> {{ $dataTypeContent->company }}</p>
                        <p><strong>Job Title:</strong> {{ $dataTypeContent->job_title }}</p>
                    </div>
                    <div class="col-md-6">
                        <h5>Review Details</h5>
                        <p><strong>Section Type:</strong>
                            <span class="badge badge-info">{{ ucfirst($dataTypeContent->section_type) }}</span>
                        </p>
                        <p><strong>{{ ucfirst($dataTypeContent->section_type) }} Title:</strong> {{ $dataTypeContent->section_title }}</p>
                        <p><strong>Rating:</strong>
                            @if($dataTypeContent->rating)
                                <span class="rating-display">
                                    @for($i = 1; $i <= 5; $i++)
                                        @if($i <= $dataTypeContent->rating)
                                            ★
                                        @else
                                            ☆
                                        @endif
                                    @endfor
                                    ({{ $dataTypeContent->rating }}/5)
                                </span>
                            @else
                                No rating provided
                            @endif
                        </p>
                        <p><strong>Status:</strong>
                            <span class="status-badge status-{{ $dataTypeContent->status }}">
                                {{ ucfirst($dataTypeContent->status) }}
                            </span>
                        </p>
                        <p><strong>Submitted:</strong> {{ $dataTypeContent->created_at->format('M d, Y H:i') }}</p>
                    </div>
                </div>
                
                @if($dataTypeContent->message)
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5>Original Message</h5>
                        <div class="message-box">
                            {{ $dataTypeContent->message }}
                        </div>
                    </div>
                </div>
                @endif
                
                @if($dataTypeContent->review_text)
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5>Review Text</h5>
                        <div class="review-box">
                            {{ $dataTypeContent->review_text }}
                        </div>
                    </div>
                </div>
                @endif
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <a href="{{ route('shuttle.scaffold_interface.edit', [$scaffoldInterface, $dataTypeContent->id]) }}" class="btn btn-primary">Edit Review</a>
                        <a href="{{ route('shuttle.scaffold_interface.index', $scaffoldInterface) }}" class="btn btn-secondary">Back to Reviews</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.rating-display {
    color: #ffc107;
    font-size: 18px;
}

.status-badge {
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.status-pending {
    background: #ffeaa7;
    color: #d63031;
}

.status-approved {
    background: #55a3ff;
    color: white;
}

.status-rejected {
    background: #fd79a8;
    color: white;
}

.message-box, .review-box {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #007bff;
    white-space: pre-wrap;
}

.review-box {
    border-left-color: #28a745;
}
</style>
@stop
