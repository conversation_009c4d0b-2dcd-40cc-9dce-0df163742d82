@extends('shuttle::admin')
@section('breadcrumbs')
    @php
        $breadCrumbs = [
            'Home' => route('shuttle.index'),
            $scaffoldInterface->display_name_plural => route('shuttle.scaffold_interface.index',$scaffoldInterface)
        ]
    @endphp
    <div class="pageTitle">
        <div class="pageTitle-title">
            <h1>{{array_key_last ($breadCrumbs)}}</h1>
        </div>
        <!-- /.pageTitle-title -->
        <div class="pageTitle-down">
            <ul>
                @foreach($breadCrumbs as $bread => $route)
                    <li class="breadcrumb-item @if($loop->last) active @endif">
                        <a href="{{$route}}">{{$bread}}</a>
                    </li>
                @endforeach
            </ul>
        </div>
    </div>
@stop

@section('main')

    <div class="row mb-5">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4>Filter Reviews</h4>
                </div>
                <div class="card-body">
                    <form class="row" method="GET">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Section Type</label>
                                <select name="section_type" id="section-type-filter" class="form-control">
                                    <option value="">All Sections</option>
                                    <option value="award" {{ request('section_type') == 'award' ? 'selected' : '' }}>Awards</option>
                                    <option value="conference" {{ request('section_type') == 'conference' ? 'selected' : '' }}>Conferences</option>
                                    <option value="course" {{ request('section_type') == 'course' ? 'selected' : '' }}>Courses</option>
                                    <option value="festival" {{ request('section_type') == 'festival' ? 'selected' : '' }}>Festivals</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Status</label>
                                <select name="status" id="status-filter" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                                    <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Rating</label>
                                <select name="rating" id="rating-filter" class="form-control">
                                    <option value="">All Ratings</option>
                                    <option value="5" {{ request('rating') == '5' ? 'selected' : '' }}>5 Stars</option>
                                    <option value="4" {{ request('rating') == '4' ? 'selected' : '' }}>4 Stars</option>
                                    <option value="3" {{ request('rating') == '3' ? 'selected' : '' }}>3 Stars</option>
                                    <option value="2" {{ request('rating') == '2' ? 'selected' : '' }}>2 Stars</option>
                                    <option value="1" {{ request('rating') == '1' ? 'selected' : '' }}>1 Star</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button class="btn btn-primary" type="submit">Filter</button>
                                    <a href="{{ route('shuttle.scaffold_interface.index', $scaffoldInterface) }}" class="btn btn-secondary">Clear</a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <scaffold-interface-table
            url="{{ route('shuttle.scaffold_interface.datatable', $scaffoldInterface) }}"
            delete-route="{{route('shuttle.scaffold_interface.destroy',['scaffold_interface' => $scaffoldInterface, 'id' => '__id'])}}"
            :columns="{{ json_encode($columns) }}"></scaffold-interface-table>

    <!-- Review Details Modal -->
    <div class="modal fade" id="reviewModal" tabindex="-1" role="dialog" aria-labelledby="reviewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h4 class="modal-title" id="reviewModalLabel">
                        <i class="iconsmind-Star"></i> Review Details
                    </h4>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="reviewModalBody">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p class="mt-2">Loading review details...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle view review button clicks
    document.addEventListener('click', function(e) {
        if (e.target.closest('.view-review-btn')) {
            e.preventDefault();
            const btn = e.target.closest('.view-review-btn');
            const reviewId = btn.getAttribute('data-id');

            // Show modal
            $('#reviewModal').modal('show');

            // Load review data
            fetch(`/mypanel/scaffold-interface/{{ $scaffoldInterface->id }}/show/${reviewId}`)
                .then(response => response.json())
                .then(data => {
                    displayReviewDetails(data);
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('reviewModalBody').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fa fa-exclamation-triangle"></i>
                            Error loading review details. Please try again.
                        </div>
                    `;
                });
        }
    });

    function displayReviewDetails(review) {
        const modalBody = document.getElementById('reviewModalBody');

        // Format rating stars
        const rating = review.rating || 0;
        const stars = '★'.repeat(rating) + '☆'.repeat(5 - rating);

        // Format date
        const createdAt = new Date(review.created_at).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        modalBody.innerHTML = `
            <div class="review-details-container">
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-card">
                            <h5><i class="fa fa-user"></i> Personal Information</h5>
                            <div class="info-item">
                                <strong>ID:</strong> <span class="badge badge-secondary">#${review.id}</span>
                            </div>
                            <div class="info-item">
                                <strong>Name:</strong> ${review.name || 'N/A'} ${review.surname || ''}
                            </div>
                            <div class="info-item">
                                <strong>Email:</strong>
                                <a href="mailto:${review.email}" class="text-primary">${review.email || 'N/A'}</a>
                            </div>
                            <div class="info-item">
                                <strong>Phone:</strong> ${review.phone || 'N/A'}
                            </div>
                            <div class="info-item">
                                <strong>Company:</strong> ${review.company || 'N/A'}
                            </div>
                            <div class="info-item">
                                <strong>Job Title:</strong> ${review.job_title || 'N/A'}
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="info-card">
                            <h5><i class="fa fa-info-circle"></i> Review Information</h5>
                            <div class="info-item">
                                <strong>Section Type:</strong>
                                <span class="badge badge-info">${review.section_type ? review.section_type.charAt(0).toUpperCase() + review.section_type.slice(1) : 'N/A'}</span>
                            </div>
                            <div class="info-item">
                                <strong>Title:</strong> ${review.section_title || 'N/A'}
                            </div>
                            <div class="info-item">
                                <strong>Rating:</strong>
                                <span class="rating-display">${stars} (${rating}/5)</span>
                            </div>
                            <div class="info-item">
                                <strong>Created At:</strong> ${createdAt}
                            </div>
                        </div>
                    </div>
                </div>

                ${review.message ? `
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="info-card">
                            <h5><i class="fa fa-envelope"></i> Original Message</h5>
                            <div class="message-content">
                                ${review.message}
                            </div>
                        </div>
                    </div>
                </div>
                ` : ''}

                ${review.review_text ? `
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="info-card review-card">
                            <h5><i class="iconsmind-Star"></i> Review Text</h5>
                            <div class="review-content">
                                ${review.review_text}
                            </div>
                        </div>
                    </div>
                </div>
                ` : ''}
            </div>
        `;
    }
});
</script>

<style>
.review-details-container {
    padding: 10px;
}

.info-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    height: 100%;
}

.info-card h5 {
    color: #495057;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #007bff;
    font-weight: 600;
}

.info-item {
    margin-bottom: 12px;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item strong {
    color: #495057;
    display: inline-block;
    min-width: 100px;
}

.rating-display {
    color: #ffc107;
    font-size: 18px;
    font-weight: bold;
}

.message-content, .review-content {
    background: white;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #007bff;
    white-space: pre-wrap;
    line-height: 1.6;
}

.review-card {
    border-left: 4px solid #28a745;
}

.review-card h5 {
    border-bottom-color: #28a745;
}

.review-content {
    border-left-color: #28a745;
}

.badge {
    font-size: 12px;
    padding: 5px 8px;
}

.modal-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.modal-title i {
    color: #ffc107;
}
</style>

@stop
