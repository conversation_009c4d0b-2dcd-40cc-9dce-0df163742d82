@push('css-vendors2')
<link rel="stylesheet" href="{{route('shuttle.assets', 'css/vendor/dataTables.bootstrap4.min.css')}}" />
<link rel="stylesheet" href="{{route('shuttle.assets', 'css/vendor/datatables.responsive.bootstrap4.min.css')}}" />
<link rel="stylesheet" href="https://cdn.datatables.net/searchpanes/2.0.2/css/searchPanes.dataTables.min.css" />
<link rel="stylesheet" href="https://cdn.datatables.net/select/1.4.0/css/select.dataTables.min.css" />
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.3/css/buttons.dataTables.min.css" />
@endpush

<scaffold-interface-filter-modal></scaffold-interface-filter-modal>

<scaffold-interface-table 
    url="{{ route('shuttle.scaffold_interface.datatable', array_merge(['scaffold_interface' => $scaffoldInterface], request()->all())) }}"
    delete-route="{{route('shuttle.scaffold_interface.destroy',['scaffold_interface' => $scaffoldInterface, 'id' => '__id'])}}"
    :columns="{{ json_encode($columns) }}"
>
</scaffold-interface-table>

@push('js')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function deleteAward(id, title) {
    Swal.fire({
        title: 'Are you sure?',
        text: `Do you want to delete "${title}"?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            // Create and submit form
            var form = $('<form method="POST" action="{{ route('shuttle.scaffold_interface.destroy', ['scaffold_interface' => $scaffoldInterface, 'id' => '__id']) }}'.replace('__id', id) + '">' +
                '<input type="hidden" name="_token" value="{{ csrf_token() }}">' +
                '<input type="hidden" name="_method" value="DELETE">' +
                '</form>');
            
            $('body').append(form);
            form.submit();
        }
    });
}
</script>
@endpush
