<?php

namespace App\Http\Controllers\Auth;

use App\Domains\PasswordReset\Services\PasswordResetService;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ResetPasswordController extends Controller
{
    protected PasswordResetService $service;

    public function __construct(
        PasswordResetService $service
    ) {
        $this->middleware('guest:api');
        $this->service = $service;
    }

    public function forgetPassword(Request $request)
    {
        try {
            $validSecs = $this->service->sendCode($request->mobile);
            return response()->json([
                'success' => true,
                'message' => 'token sent to mobile number',
                'validSecs' => $validSecs
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'cannot send sms'
            ]);
        }
    }


    public function resetPassword(Request $request)
    {
        $request->validate([
            'code'       => 'required',
            'password'   => 'required|min:8',
        ]);

        try {
            $this->service->reset(
                $request->code,
                $request->password
            );

            return response()->json([
                'success' => true,
                'message' => 'password changed successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Code expired or doesn`t exist'
            ]);
        }
    }
}
