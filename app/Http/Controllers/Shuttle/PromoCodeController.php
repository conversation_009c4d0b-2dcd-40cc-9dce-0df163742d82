<?php

namespace App\Http\Controllers\Shuttle;

use App\Models\PromoCode;
use Illuminate\Http\Request;
use Sina\Shuttle\Http\Controllers\ShuttleController;
use Sina\Shuttle\Http\Resources\DataTableResource;
use Sina\Shuttle\Models\ScaffoldInterface;
use Carbon\Carbon;

class PromoCodeController extends ShuttleController
{


    /**
     * Generate a new promo code
     */
    public function generate(Request $request)
    {
        $request->validate([
            'discount_percentage' => 'required|numeric|min:1|max:100',
            'expires_in_days' => 'integer|min:1|max:365'
        ]);

        $expiresInDays = $request->input('expires_in_days', 5); // Default 5 days
        
        $promoCode = PromoCode::create([
            'code' => PromoCode::generateCode(),
            'discount_percentage' => $request->discount_percentage,
            'expires_at' => Carbon::now()->addDays($expiresInDays),
            'is_active' => true,
            'max_usage' => $request->max_usage // Can be null for unlimited
        ]);

        return redirect()->back()->with([
            'message' => "Promo code '{$promoCode->code}' generated successfully with {$promoCode->discount_percentage}% discount!",
            'alert-type' => 'success',
        ]);
    }

    /**
     * Toggle promo code status
     */
    public function toggle(PromoCode $promoCode)
    {
        $promoCode->update(['is_active' => !$promoCode->is_active]);
        
        $status = $promoCode->is_active ? 'activated' : 'deactivated';
        
        return redirect()->back()->with([
            'message' => "Promo code '{$promoCode->code}' has been {$status}!",
            'alert-type' => 'success',
        ]);
    }

    /**
     * DataTable for admin panel
     */
    public function datatable(Request $request, ScaffoldInterface $scaffoldInterface)
    {
        return $this->getDataTableResource(
            DataTableResource::newInstance()
                ->setScaffoldInterface($scaffoldInterface, function ($query) use ($request) {
                    if (!empty($request->status)) {
                        switch ($request->status) {
                            case 'active':
                                $query->where('is_active', true)
                                      ->where('expires_at', '>', now());
                                break;
                            case 'expired':
                                $query->where('expires_at', '<', now());
                                break;
                            case 'inactive':
                                $query->where('is_active', false);
                                break;
                        }
                    }
                    
                    if (!empty($request->q)) {
                        $query->where('code', 'like', '%' . $request->q . '%');
                    }
                    
                    return $query->latest();
                })
                ->addAction(fn ($data) => '<a href="' . route('shuttle.promo-codes.toggle', $data->id) . '" class="btn btn-bootstrap-padding ' . ($data->is_active ? 'btn-warning' : 'btn-success') . '">' . ($data->is_active ? 'Deactivate' : 'Activate') . '</a>')
                ->addAction(fn ($data) => '<button type="button" class="btn btn-bootstrap-padding btn-danger remove-item" data-id="'.$data->id.'"><i class="glyph-icon simple-icon-trash"></i></button>')
        )->json();
    }
}
