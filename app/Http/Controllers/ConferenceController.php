<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\ConferenceCategory;
use App\Models\Location;
use App\Models\Client;
use App\Models\Course;
use App\Models\Conference;
use App\Models\ConferencePackage;
use Illuminate\Http\Request;
use DB;

class ConferenceController extends Controller
{
    public function index(Request $request)
    {
        $categories = ConferenceCategory::withCount('conferences')->get();

        $clients = Client::all();

        $locations = Location::all();

//        $conferences = ConferencePackage::whereIn('conference_id', function ($q){
//            $q->select('id')->from('conferences');
//        })->whereNotNull('start_at')->whereNotNull('end_at')
//            ->with('conference.category', 'conference.location');
//
//        if(!empty($request->category_id)){
//            $conferences = $conferences->whereIn('conference_id', function ($q) use ($request) {
//                $q->select('conferences.id')->from('conferences')->where('category_id', $request->category_id);
//            });
//        }
//
//        if(!empty($request->location_id)){
//            $conferences = $conferences->where('location_id', $request->location_id);
//        }
//
//        if(!empty($request->year)){
//            $conferences = $conferences->where(DB::raw('YEAR(start_at)'), $request->year);
//        }
//
//        if(!empty($request->month)){
//            $conferences = $conferences->where(DB::raw('MONTH(start_at)'), $request->year);
//        }
//
//        $conferences = $conferences->orderBy('start_at', 'asc')->paginate(8);
        $conferences = Conference::query()
            ->whereIn('id', function ($q){
                $q->select('conference_id')->from('conference_packages')->whereNotNull('start_at')->whereNotNull('end_at');
            })->whereHas('packages', function ($q) {
                $q->where('start_at', '>', now());
            });

        if(!empty($request->category_id)) {
            $conferences = $conferences->where('category_id', $request->category_id);
        }
        if(!empty($request->location_id)){
           $conferences = $conferences->where('location_id', $request->location_id);
        }
        if(!empty($request->year)){
             $conferences = $conferences->whereHas('packages', function ($q) use ($request) {
                $q->whereYear('start_at', $request->year);
            });
        }

        if(!empty($request->month)){
            $month = is_numeric($request->month)
                ? $request->month
                : date('n', strtotime("1 {$request->month}"));
             $conferences = $conferences->whereHas('packages', function($q) use ($month) {
                $q->whereMonth('start_at', $month);
            });
        }

        $conferences = $conferences
//            ->leftJoin('conference_packages', function ($join){
//            $join->on('conference_id', 'conferences.id')->whereNotNull('start_at')->whereNotNull('end_at');
//        })
            ->with('category', 'location', 'upComing', 'packages')
            ->paginate(8);
        // dd($conferences->toArray());
        $title = __("common.conferences");
        return view('conference.index', compact('categories', 'conferences', 'clients', 'locations', 'title'));
    }

    public function show(Request $request)
    {
        $title = preg_replace('/[^a-z0-9]/', '', $request->conference_title);
        $conference = Conference::where(DB::raw("LOWER(REGEXP_REPLACE(title, '[^0-9a-z]', ''))"), $title)
        ->first();
        $upcomingPackages = $conference->packages()
        ->where('start_at', '>', now())
        ->get();

        $clients = Client::all();
        $categories = ConferenceCategory::all();
        $title = $conference->title;

        return view('conference.show', compact('conference','clients', 'upcomingPackages', 'categories', 'title'));
    }

}
