<?php

namespace App\Http\Controllers;

use DB;
use App\Models\Form;
use App\Models\Course;
use App\Models\Enquiry;
use App\Models\Review;
use App\Models\Category;
use App\Models\FormData;
use App\Models\Location;
use App\Models\Conference;
use App\Models\Awward;
use Illuminate\Http\Request;
use App\Models\ConferencePackage;
use App\Http\Controllers\Controller;

use Illuminate\Support\Facades\Mail;
use App\Http\Requests\SendEmailRequest;
use Illuminate\Support\Facades\Storage;

class ContactController extends Controller
{
    public function enquiry(Request $request)
    {
        // Debug: Log all request data
        \Log::info('=== ENQUIRY REQUEST START ===');
        \Log::info('Request Type: ' . ($request->type ?? 'NULL'));
        \Log::info('All Request Data:', $request->all());
        \Log::info('=== ENQUIRY REQUEST END ===');

        // Save enquiry
        Enquiry::create($request->all());

        // If there's review data, save the review for any section type
        if ($request->review_text || $request->rating) {
            $reviewData = [
                'name' => $request->name ?? $request->first_name,
                'surname' => $request->surname ?? $request->last_name,
                'email' => $request->email,
                'phone' => $request->phone ?? $request->mobile,
                'company' => $request->company,
                'job_title' => $request->job_title ?? $request->job,
                'message' => $request->message,
                'review_text' => $request->review_text,
                'rating' => $request->rating,
                'more_info' => $request->more_info,
                'know_by' => $request->know_by,
                'status' => 'pending'
            ];

            // Determine section type and set appropriate fields
            switch ($request->type) {
                case 'award':
                    $reviewData['model_type'] = Awward::class;
                    $reviewData['model_id'] = $request->award_id;
                    $reviewData['section_type'] = 'award';
                    $reviewData['section_id'] = $request->award_id;
                    $reviewData['section_title'] = $request->award_title;
                    break;

                case 'conference':
                    $reviewData['model_type'] = Conference::class;
                    $reviewData['model_id'] = $request->conference_id;
                    $reviewData['section_type'] = 'conference';
                    $reviewData['section_id'] = $request->conference_id;
                    $reviewData['section_title'] = $request->conference_title;
                    break;

                case 'course':
                    $reviewData['model_type'] = Course::class;
                    $reviewData['model_id'] = $request->course_id;
                    $reviewData['section_type'] = 'course';
                    $reviewData['section_id'] = $request->course_id;
                    $reviewData['section_title'] = $request->course_title;
                    break;

                case 'festival':
                    $reviewData['model_type'] = 'App\Models\Festival';
                    $reviewData['model_id'] = $request->festival_id;
                    $reviewData['section_type'] = 'festival';
                    $reviewData['section_id'] = $request->festival_id;
                    $reviewData['section_title'] = $request->festival_title;
                    break;

                default:
                    $reviewData['section_type'] = $request->type ?? 'unknown';
                    break;
            }

            Review::create($reviewData);
        }

        return back();
    }

    public function form(Request $request, Form $form)
    {
        // Save to FormData table (existing functionality)
        FormData::create([
            'form_id' => $form->id,
            'data' => $request->all(),
            'model_type' => match($request->model_type){
                'conference' => Conference::class,
                'awward' => Awward::class,
                default => null
            },
            'model_id' => $request->model_id
        ]);

        // Also save to Enquiries table for admin panel visibility
        Enquiry::create([
            'first_name' => $request->input('First_Name') ?? $request->input('first_name'),
            'last_name' => $request->input('Last_Name') ?? $request->input('last_name'),
            'company' => $request->input('Company') ?? $request->input('company'),
            'job' => $request->input('Job') ?? $request->input('job'),
            'email' => $request->input('Email') ?? $request->input('email'),
            'mobile' => $request->input('Mobile') ?? $request->input('mobile'),
            'model_type' => match($request->model_type){
                'conference' => Conference::class,
                'awward' => Awward::class,
                default => null
            },
            'model_id' => $request->model_id,
            'type' => 'form_submission',
            'know_by' => $request->input('know_by') ?? $request->input('Know_by')
        ]);

        if($form->pdf){
            return Storage::download($form->pdf);
            return response()->download(Storage::url('app/'.$form->pdf), 'pdf', ['location' => '/home']);
        }
        return back();
    }

    public function send(SendEmailRequest  $request)
    {
        $validated = $request->validated();

        // Send email
        Mail::send([], [], function ($message) use ($validated) {
            $message->to('<EMAIL>')
                ->subject($validated['subject'])
                ->setBody(
                    "Name: {$validated['name']}\n" .
                    "Email: {$validated['email']}\n" .
                    "Phone: {$validated['number']}\n\n" .
                    "Message:\n{$validated['message']}",
                    'text/plain'
                );
        });

        return back()->with('success', 'Message sent successfully!');
    }

}
