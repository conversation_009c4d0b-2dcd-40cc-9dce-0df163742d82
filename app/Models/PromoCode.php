<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class PromoCode extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    protected $casts = [
        'expires_at' => 'datetime',
        'is_active' => 'boolean',
        'discount_percentage' => 'decimal:2'
    ];



    /**
     * Generate a unique promo code
     */
    public static function generateCode($length = 8)
    {
        do {
            $code = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, $length));
        } while (self::where('code', $code)->exists());

        return $code;
    }

    /**
     * Check if promo code is valid
     */
    public function isValid()
    {
        return $this->is_active 
            && $this->expires_at > now() 
            && ($this->max_usage === null || $this->usage_count < $this->max_usage);
    }

    /**
     * Use the promo code (increment usage count)
     */
    public function use()
    {
        if ($this->isValid()) {
            $this->increment('usage_count');
            return true;
        }
        return false;
    }

    /**
     * Calculate discount amount
     */
    public function calculateDiscount($amount)
    {
        if (!$this->isValid()) {
            return 0;
        }

        return ($amount * $this->discount_percentage) / 100;
    }

    /**
     * Get status text
     */
    public function getStatusAttribute()
    {
        if (!$this->is_active) {
            return 'Inactive';
        }
        
        if ($this->expires_at < now()) {
            return 'Expired';
        }
        
        if ($this->max_usage && $this->usage_count >= $this->max_usage) {
            return 'Used Up';
        }
        
        return 'Active';
    }
}
