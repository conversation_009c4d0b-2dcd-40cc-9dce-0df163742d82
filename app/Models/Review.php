<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Review extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    /**
     * Default ordering by newest first
     */
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('ordered', function ($builder) {
            $builder->orderBy('created_at', 'desc');
        });
    }

    public function model()
    {
        return $this->morphTo();
    }

    // Relationships for different section types
    public function award()
    {
        return $this->belongsTo(Awward::class, 'section_id')->where('section_type', 'award');
    }

    public function conference()
    {
        return $this->belongsTo(Conference::class, 'section_id')->where('section_type', 'conference');
    }

    public function course()
    {
        return $this->belongsTo(Course::class, 'section_id')->where('section_type', 'course');
    }

    public function festival()
    {
        return $this->belongsTo('App\Models\Festival', 'section_id')->where('section_type', 'festival');
    }

    // Get the related section item dynamically
    public function getRelatedSectionAttribute()
    {
        switch ($this->section_type) {
            case 'award':
                return $this->award;
            case 'conference':
                return $this->conference;
            case 'course':
                return $this->course;
            case 'festival':
                return $this->festival;
            default:
                return null;
        }
    }

    // Scope methods for filtering by section type
    public function scopeAwards($query)
    {
        return $query->where('section_type', 'award');
    }

    public function scopeConferences($query)
    {
        return $query->where('section_type', 'conference');
    }

    public function scopeCourses($query)
    {
        return $query->where('section_type', 'course');
    }

    public function scopeFestivals($query)
    {
        return $query->where('section_type', 'festival');
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }
}
