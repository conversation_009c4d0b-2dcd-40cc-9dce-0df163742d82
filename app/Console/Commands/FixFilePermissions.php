<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class FixFilePermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:permissions {--check : Only check permissions without fixing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix file permissions for views/sections and other critical directories';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $checkOnly = $this->option('check');
        
        $this->info('Business Eagles - File Permissions ' . ($checkOnly ? 'Check' : 'Fix'));
        $this->info('================================================');
        
        $paths = [
            'resources/views/sections' => ['dir' => 0755, 'file' => 0644],
            'resources/views/components' => ['dir' => 0755, 'file' => 0644],
            'storage/app/public' => ['dir' => 0755, 'file' => 0644],
            'storage/logs' => ['dir' => 0755, 'file' => 0644],
            'bootstrap/cache' => ['dir' => 0755, 'file' => 0644],
        ];
        
        $issues = [];
        
        foreach ($paths as $path => $permissions) {
            $fullPath = base_path($path);
            $this->line("Checking: $path");
            
            // Check if directory exists
            if (!File::isDirectory($fullPath)) {
                $issues[] = "Directory does not exist: $path";
                $this->warn("  - Directory does not exist");
                
                if (!$checkOnly) {
                    $this->info("  - Creating directory...");
                    try {
                        File::makeDirectory($fullPath, $permissions['dir'], true);
                        $this->info("  - Directory created successfully");
                    } catch (\Exception $e) {
                        $this->error("  - Failed to create directory: " . $e->getMessage());
                        $issues[] = "Failed to create directory: $path - " . $e->getMessage();
                    }
                }
                continue;
            }
            
            // Check directory permissions
            $currentPerms = fileperms($fullPath) & 0777;
            if ($currentPerms !== $permissions['dir']) {
                $issues[] = "Directory permissions incorrect: $path (current: " . decoct($currentPerms) . ", expected: " . decoct($permissions['dir']) . ")";
                $this->warn("  - Directory permissions: " . decoct($currentPerms) . " (expected: " . decoct($permissions['dir']) . ")");
                
                if (!$checkOnly) {
                    try {
                        chmod($fullPath, $permissions['dir']);
                        $this->info("  - Directory permissions fixed");
                    } catch (\Exception $e) {
                        $this->error("  - Failed to fix directory permissions: " . $e->getMessage());
                        $issues[] = "Failed to fix directory permissions: $path - " . $e->getMessage();
                    }
                }
            } else {
                $this->info("  - Directory permissions OK");
            }
            
            // Check if directory is writable
            if (!is_writable($fullPath)) {
                $issues[] = "Directory not writable: $path";
                $this->error("  - Directory is not writable");
            } else {
                $this->info("  - Directory is writable");
            }
            
            // Check files in directory
            if (File::isDirectory($fullPath)) {
                $files = File::allFiles($fullPath);
                $fileIssues = 0;
                
                foreach ($files as $file) {
                    $filePath = $file->getPathname();
                    $currentFilePerms = fileperms($filePath) & 0777;
                    
                    if ($currentFilePerms !== $permissions['file']) {
                        $fileIssues++;
                        if (!$checkOnly) {
                            try {
                                chmod($filePath, $permissions['file']);
                            } catch (\Exception $e) {
                                $issues[] = "Failed to fix file permissions: $filePath - " . $e->getMessage();
                            }
                        }
                    }
                }
                
                if ($fileIssues > 0) {
                    if ($checkOnly) {
                        $this->warn("  - $fileIssues files have incorrect permissions");
                        $issues[] = "$fileIssues files in $path have incorrect permissions";
                    } else {
                        $this->info("  - Fixed permissions for $fileIssues files");
                    }
                } else {
                    $this->info("  - All file permissions OK");
                }
            }
        }
        
        $this->line('');
        
        if (empty($issues)) {
            $this->info('✅ All permissions are correct!');
            return 0;
        } else {
            $this->error('❌ Found ' . count($issues) . ' permission issues:');
            foreach ($issues as $issue) {
                $this->line("  - $issue");
            }
            
            if ($checkOnly) {
                $this->line('');
                $this->info('Run without --check flag to fix these issues:');
                $this->info('php artisan fix:permissions');
            }
            
            return 1;
        }
    }
}
