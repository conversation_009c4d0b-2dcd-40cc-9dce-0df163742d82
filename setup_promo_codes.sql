-- Setup Promo Codes System
-- Run this SQL file to create promo codes functionality

-- 1. Create promo_codes table
CREATE TABLE `promo_codes` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `code` varchar(255) NOT NULL,
  `discount_percentage` decimal(5,2) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `expires_at` timestamp NOT NULL,
  `usage_count` int(11) NOT NULL DEFAULT 0,
  `max_usage` int(11) NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `promo_codes_code_unique` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Add Promo Codes to admin panel
INSERT INTO `shuttle_scaffold_interfaces` (`id`, `name`, `icon`, `slug`, `display_name_singular`, `display_name_plural`, `migration`, `model`, `translation_model`, `controller`, `menuable`, `by_shuttle`, `created_at`, `updated_at`) VALUES 
(19, 'promo_codes', 'iconsmind-Tag', 'promo-codes', 'Promo Code', 'Promo Codes', NULL, 'App\\Models\\PromoCode', NULL, 'App\\Http\\Controllers\\Shuttle\\PromoCodeController', 0, 0, NOW(), NOW());

-- 3. Add scaffold interface rows for promo codes
INSERT INTO `shuttle_scaffold_interface_rows` (`rowable_type`, `rowable_id`, `parent_id`, `field`, `type`, `display_name`, `required`, `browse`, `read`, `edit`, `add`, `delete`, `details`, `ord`, `last_upd`) VALUES 
('Sina\\Shuttle\\Models\\ScaffoldInterface', 19, 0, 'id', 'text', 'ID', 0, 1, 1, 0, 0, 0, '{}', 1, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 19, 0, 'code', 'text', 'Code', 1, 1, 1, 0, 0, 0, '{}', 2, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 19, 0, 'discount_percentage', 'number', 'Discount %', 1, 1, 1, 1, 1, 0, '{"step": "0.01", "min": "0", "max": "100"}', 3, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 19, 0, 'is_active', 'checkbox', 'Active', 0, 1, 1, 1, 1, 0, '{}', 4, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 19, 0, 'expires_at', 'timestamp', 'Expires At', 1, 1, 1, 1, 1, 0, '{}', 5, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 19, 0, 'usage_count', 'number', 'Used', 0, 1, 1, 0, 0, 0, '{}', 6, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 19, 0, 'max_usage', 'number', 'Max Usage', 0, 1, 1, 1, 1, 0, '{}', 7, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 19, 0, 'created_at', 'timestamp', 'Created At', 0, 1, 1, 0, 0, 0, '{}', 8, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', 19, 0, 'updated_at', 'timestamp', 'Updated At', 0, 0, 1, 0, 0, 0, '{}', 9, 0);

-- 4. Create a sample promo code for testing
INSERT INTO `promo_codes` (`code`, `discount_percentage`, `is_active`, `expires_at`, `usage_count`, `max_usage`, `created_at`, `updated_at`) VALUES 
('WELCOME10', 10.00, 1, DATE_ADD(NOW(), INTERVAL 30 DAY), 0, NULL, NOW(), NOW()),
('SAVE20', 20.00, 1, DATE_ADD(NOW(), INTERVAL 7 DAY), 0, 100, NOW(), NOW());
