<?php
/**
 * <PERSON><PERSON><PERSON> to fix file permissions for the Business Eagles application
 * This script should be run on the production server to fix permission issues
 * with the resources/views/sections directory
 */

// Define the paths that need proper permissions
$paths = [
    'resources/views/sections',
    'resources/views/sections/en',
    'resources/views/sections/ka', 
    'resources/views/sections/ru',
    'resources/views/components',
    'storage/app/public',
    'storage/logs',
    'bootstrap/cache'
];

echo "Business Eagles - File Permissions Fix Script\n";
echo "=============================================\n\n";

// Check if running as root or with sudo
if (posix_getuid() !== 0) {
    echo "Warning: This script should be run with sudo privileges for best results.\n";
    echo "Usage: sudo php fix-permissions.php\n\n";
}

foreach ($paths as $path) {
    echo "Processing: $path\n";
    
    // Create directory if it doesn't exist
    if (!is_dir($path)) {
        echo "  - Creating directory: $path\n";
        if (!mkdir($path, 0755, true)) {
            echo "  - ERROR: Could not create directory: $path\n";
            continue;
        }
    }
    
    // Set directory permissions
    echo "  - Setting directory permissions to 755\n";
    if (!chmod($path, 0755)) {
        echo "  - ERROR: Could not set permissions for directory: $path\n";
    }
    
    // Set file permissions for existing files
    if (is_dir($path)) {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($path, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $item) {
            if ($item->isDir()) {
                echo "  - Setting directory permissions: " . $item->getPathname() . "\n";
                chmod($item->getPathname(), 0755);
            } else {
                echo "  - Setting file permissions: " . $item->getPathname() . "\n";
                chmod($item->getPathname(), 0644);
            }
        }
    }
    
    echo "  - Completed: $path\n\n";
}

// Additional commands for web server ownership (uncomment if needed)
echo "Additional commands to run manually if needed:\n";
echo "=============================================\n";
echo "# Set ownership to web server user (replace 'www-data' with your web server user):\n";
echo "sudo chown -R www-data:www-data resources/views/sections/\n";
echo "sudo chown -R www-data:www-data resources/views/components/\n";
echo "sudo chown -R www-data:www-data storage/\n";
echo "sudo chown -R www-data:www-data bootstrap/cache/\n\n";

echo "# Alternative ownership commands for different server setups:\n";
echo "# For Apache: sudo chown -R apache:apache [directories]\n";
echo "# For Nginx: sudo chown -R nginx:nginx [directories]\n";
echo "# For shared hosting: sudo chown -R [your-username]:[your-group] [directories]\n\n";

echo "Script completed!\n";
echo "If you're still experiencing permission issues, please run the ownership commands above.\n";
?>
