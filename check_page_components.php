<?php
// Simple script to check page components
// Run this from browser: https://business-eagles.com/check_page_components.php

require_once 'vendor/autoload.php';

try {
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();

    use Sina\Shuttle\Models\Page;

    $pageId = 22;
    $page = Page::with(['components' => function($query) {
        $query->orderBy('position');
    }])->find($pageId);

    if (!$page) {
        echo "Page with ID $pageId not found";
        exit;
    }

    echo "<h2>Page: {$page->title}</h2>";
    echo "<p>URL: {$page->url}</p>";
    echo "<p>Total Components: " . $page->components->count() . "</p>";

    if ($page->components->count() > 0) {
        echo "<h3>Components:</h3>";
        echo "<ul>";
        foreach ($page->components as $index => $component) {
            echo "<li>";
            echo "[$index] Component: <strong>{$component->name}</strong><br>";
            echo "Locale: {$component->pivot->locale}<br>";
            echo "Position: {$component->pivot->position}<br>";
            echo "Settings: " . json_encode($component->pivot->setting) . "<br>";
            echo "</li><br>";
        }
        echo "</ul>";
        
        // Generate the template content
        echo "<h3>Generated Template Content:</h3>";
        echo "<pre>";
        foreach ($page->components as $index => $component) {
            echo htmlspecialchars('<x-shuttle-dynamic-component :name="$page->components[' . $index . ']->name" :c="$page->components[' . $index . ']" :data="$page->components[' . $index . ']->pivot->setting"></x-shuttle-dynamic-component>') . "\n";
        }
        echo "</pre>";
    } else {
        echo "<p>No components found for this page.</p>";
    }

} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
