-- Fix Promo Codes scaffold interface fields
-- This fixes the getTranslatedAttribute error by using text fields instead of number fields

-- Delete existing rows for promo codes
DELETE FROM shuttle_scaffold_interface_rows WHERE rowable_id = (SELECT id FROM shuttle_scaffold_interfaces WHERE slug = 'promo-codes');

-- Add corrected scaffold interface rows for promo codes
INSERT INTO `shuttle_scaffold_interface_rows` (`rowable_type`, `rowable_id`, `parent_id`, `field`, `type`, `display_name`, `required`, `browse`, `read`, `edit`, `add`, `delete`, `details`, `ord`, `last_upd`) VALUES 
('Sina\\Shuttle\\Models\\ScaffoldInterface', (SELECT id FROM shuttle_scaffold_interfaces WHERE slug = 'promo-codes'), 0, 'id', 'text', 'ID', 0, 1, 1, 0, 0, 0, '{}', 1, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', (SELECT id FROM shuttle_scaffold_interfaces WHERE slug = 'promo-codes'), 0, 'code', 'text', 'Code', 1, 1, 1, 1, 1, 0, '{"placeholder": "Auto-generated"}', 2, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', (SELECT id FROM shuttle_scaffold_interfaces WHERE slug = 'promo-codes'), 0, 'discount_percentage', 'text', 'Discount %', 1, 1, 1, 1, 1, 0, '{"placeholder": "e.g., 10.50"}', 3, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', (SELECT id FROM shuttle_scaffold_interfaces WHERE slug = 'promo-codes'), 0, 'is_active', 'checkbox', 'Active', 0, 1, 1, 1, 1, 0, '{"on": "Active", "off": "Inactive", "checked": true}', 4, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', (SELECT id FROM shuttle_scaffold_interfaces WHERE slug = 'promo-codes'), 0, 'expires_at', 'timestamp', 'Expires At', 1, 1, 1, 1, 1, 0, '{}', 5, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', (SELECT id FROM shuttle_scaffold_interfaces WHERE slug = 'promo-codes'), 0, 'usage_count', 'text', 'Used', 0, 1, 1, 0, 0, 0, '{"default": "0"}', 6, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', (SELECT id FROM shuttle_scaffold_interfaces WHERE slug = 'promo-codes'), 0, 'max_usage', 'text', 'Max Usage', 0, 1, 1, 1, 1, 0, '{"placeholder": "Leave empty for unlimited"}', 7, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', (SELECT id FROM shuttle_scaffold_interfaces WHERE slug = 'promo-codes'), 0, 'created_at', 'timestamp', 'Created At', 0, 1, 1, 0, 0, 0, '{}', 8, 0),
('Sina\\Shuttle\\Models\\ScaffoldInterface', (SELECT id FROM shuttle_scaffold_interfaces WHERE slug = 'promo-codes'), 0, 'updated_at', 'timestamp', 'Updated At', 0, 0, 1, 0, 0, 0, '{}', 9, 0);
