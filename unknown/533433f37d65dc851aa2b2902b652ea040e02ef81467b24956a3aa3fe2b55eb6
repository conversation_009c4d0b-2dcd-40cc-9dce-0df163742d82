@extends('app')

@push('seo')
    <title>{{ $post->title }} - Business-Eagles.com</title>
    <meta name="keywords" content="{{ $post->keyword }}">
    <meta name="description" content="{{ $post->description }}">
    <meta property="og:title" content="{{ $post->title }}">
    <meta property="og:description" content="{{ $post->description }}">
    <meta property="og:image" content="{{ $post->image ? Storage::url($post->image) : asset('assets/img/default-blog.jpg') }}">
    <meta property="og:url" content="{{ url()->current() }}">
@endpush

@section('content')
    @include('includes.header')
    
    <section class="cover">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="spead">
                        <h1>{{ $post->title }}</h1>
                        <p style="color: white;">{{ $post->description }}</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="section blog-single">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <article class="blog-post">
                        @if($post->image)
                            <div class="blog-image">
                                <img src="{{ Storage::url($post->image) }}" class="img-fluid" alt="{{ $post->title }}">
                            </div>
                        @endif
                        
                        <div class="blog-content">
                            <div class="blog-meta">
                                <span class="blog-date">
                                    <i class="icofont-calendar"></i>
                                    {{ $post->created_at->format('M d, Y') }}
                                </span>
                            </div>
                            
                            <div class="blog-text">
                                {!! $post->text !!}
                            </div>
                        </div>
                    </article>
                </div>
                
                <div class="col-md-4">
                    <div class="blog-sidebar">
                        <!-- Search Widget -->
                        <div class="widget search-widget">
                            <h3>Search</h3>
                            <form action="{{ route('search') }}" method="GET" class="search-form">
                                <div class="input-group">
                                    <input type="text" name="q" class="form-control" placeholder="Enter keyword..." value="{{ request('q') }}">
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="icofont-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Blogs Section -->
    @php
        $relatedBlogs = \App\Models\Blog::where('id', '!=', $post->id)
            ->latest()
            ->limit(3)
            ->get();
    @endphp

    @if($relatedBlogs->count() > 0)
    <section class="section related-blogs">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="section-title">
                        <span>Blog</span>
                        <h1>Related</h1>
                    </div>
                </div>
            </div>
            <div class="row">
                @foreach($relatedBlogs as $relatedBlog)
                    <div class="col-md-4">
                        <article class="pbmit-service-style-3">
                            <div class="pbminfotech-post-item">
                                <div class="pbmit-featured-wrapper">
                                    <img src="{{ Storage::url($relatedBlog->image) }}" class="img-fluid" alt="{{ $relatedBlog->title }}">
                                </div>
                                <div class="pbminfotech-box-content">
                                    <div class="pbminfotech-box-content-inner">
                                        <div class="pbmit-service-cat">
                                            <a href="{{ route('blog.show', $relatedBlog->id) }}" rel="tag">{{ $relatedBlog->title }}</a>
                                        </div>
                                        <h3 class="pbmit-service-title">
                                            <a href="{{ route('blog.show', $relatedBlog->id) }}">{{ $relatedBlog->title }}</a>
                                        </h3>
                                        <div class="pbmit-service-content">
                                            <p>{{ Str::limit(strip_tags($relatedBlog->text), 100) }}</p>
                                        </div>
                                        <div class="pbmit-service-btn">
                                            <a href="{{ route('blog.show', $relatedBlog->id) }}">
                                                <span>{{ $relatedBlog->created_at->format('M d, Y') }}</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </article>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

@stop

@push('css')
<style>
.blog-single {
    padding: 80px 0;
}

.blog-post {
    background: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.blog-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.blog-content {
    padding: 30px;
}

.blog-meta {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.blog-date {
    color: #666;
    font-size: 14px;
}

.blog-date i {
    margin-right: 5px;
    color: #007bff;
}

.blog-text {
    line-height: 1.8;
    color: #333;
}

.blog-text p {
    margin-bottom: 20px;
}

.blog-sidebar {
    padding-left: 30px;
}

.widget {
    background: #fff;
    padding: 30px;
    margin-bottom: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.widget h3 {
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.search-form .form-control {
    border: 1px solid #ddd;
    border-radius: 5px 0 0 5px;
    padding: 12px 15px;
}

.search-form .btn {
    border-radius: 0 5px 5px 0;
    padding: 12px 20px;
}

.related-blogs {
    background: #f8f9fa;
    padding: 80px 0;
}

.section-title {
    text-align: center;
    margin-bottom: 50px;
}

.section-title span {
    color: #007bff;
    font-size: 16px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.section-title h1 {
    font-size: 36px;
    font-weight: 700;
    color: #333;
    margin-top: 10px;
}

@media (max-width: 768px) {
    .blog-sidebar {
        padding-left: 0;
        margin-top: 40px;
    }
}
</style>
@endpush
