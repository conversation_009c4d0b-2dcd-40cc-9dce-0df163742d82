<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPromoFieldsToTransactionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->string('promo_code')->nullable()->after('payment_method');
            $table->decimal('promo_discount_percentage', 5, 2)->nullable()->after('promo_code');
            $table->decimal('promo_discount_amount', 8, 2)->nullable()->after('promo_discount_percentage');
            $table->string('stripe_charge_id')->nullable()->after('promo_discount_amount');
            $table->string('job_title')->nullable()->after('job');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropColumn(['promo_code', 'promo_discount_percentage', 'promo_discount_amount', 'stripe_charge_id', 'job_title']);
        });
    }
}
