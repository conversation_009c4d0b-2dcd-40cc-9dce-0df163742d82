<?php

namespace App\Http\Controllers;

use App\Models\PromoCode;
use Illuminate\Http\Request;

class PromoCodeController extends Controller
{
    /**
     * Validate promo code via AJAX
     */
    public function validate(Request $request)
    {
        $request->validate([
            'code' => 'required|string',
            'amount' => 'required|numeric|min:0'
        ]);

        $promoCode = PromoCode::where('code', strtoupper($request->code))->first();

        if (!$promoCode) {
            return response()->json([
                'valid' => false,
                'message' => 'Promo code not found.'
            ], 404);
        }

        if (!$promoCode->isValid()) {
            $message = 'Promo code is ';
            if (!$promoCode->is_active) {
                $message .= 'inactive.';
            } elseif ($promoCode->expires_at < now()) {
                $message .= 'expired.';
            } elseif ($promoCode->max_usage && $promoCode->usage_count >= $promoCode->max_usage) {
                $message .= 'no longer available.';
            }

            return response()->json([
                'valid' => false,
                'message' => $message
            ], 400);
        }

        $discountAmount = $promoCode->calculateDiscount($request->amount);
        $finalAmount = $request->amount - $discountAmount;

        return response()->json([
            'valid' => true,
            'message' => "Promo code applied! {$promoCode->discount_percentage}% discount.",
            'discount_percentage' => $promoCode->discount_percentage,
            'discount_amount' => number_format($discountAmount, 2),
            'original_amount' => number_format($request->amount, 2),
            'final_amount' => number_format($finalAmount, 2),
            'code' => $promoCode->code
        ]);
    }

    /**
     * Apply promo code (use it)
     */
    public function apply(Request $request)
    {
        $request->validate([
            'code' => 'required|string'
        ]);

        $promoCode = PromoCode::where('code', strtoupper($request->code))->first();

        if (!$promoCode || !$promoCode->isValid()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid or expired promo code.'
            ], 400);
        }

        // Use the promo code
        $promoCode->use();

        return response()->json([
            'success' => true,
            'message' => 'Promo code applied successfully!',
            'code' => $promoCode->code,
            'discount_percentage' => $promoCode->discount_percentage
        ]);
    }
}
